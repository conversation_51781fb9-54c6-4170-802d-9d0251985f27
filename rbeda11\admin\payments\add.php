<?php
$page_title = "إضافة دفعة جديدة";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $member_id = (int)$_POST['member_id'];
    $subscription_id = !empty($_POST['subscription_id']) ? (int)$_POST['subscription_id'] : null;
    $amount = (float)$_POST['amount'];
    $payment_method = sanitize_input($_POST['payment_method']);
    $payment_date = sanitize_input($_POST['payment_date']);
    $status = sanitize_input($_POST['status']);
    $notes = sanitize_input($_POST['notes']);

    // التحقق من صحة البيانات
    if ($member_id <= 0 || $amount <= 0 || empty($payment_method) || empty($payment_date) || empty($status)) {
        $error = 'جميع الحقول المطلوبة يجب تعبئتها بقيم صحيحة';
    } else {
        // التحقق من وجود العضو
        $member_check_sql = "SELECT id FROM members WHERE id = ?";
        $member_stmt = $conn->prepare($member_check_sql);
        $member_stmt->bind_param("i", $member_id);
        $member_stmt->execute();
        $member_exists = $member_stmt->get_result()->num_rows > 0;
        $member_stmt->close();

        if (!$member_exists) {
            $error = 'العضو المحدد غير موجود';
        } else {
            // إدخال الدفعة الجديدة
            $insert_sql = "INSERT INTO payments (member_id, subscription_id, amount, payment_method, payment_date, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("iidssss", $member_id, $subscription_id, $amount, $payment_method, $payment_date, $status, $notes);

            if ($insert_stmt->execute()) {
                $_SESSION['success'] = 'تم إضافة الدفعة بنجاح';
                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء إضافة الدفعة: ' . $conn->error;
            }
            $insert_stmt->close();
        }
    }
}

// جلب قائمة الأعضاء
$members_sql = "SELECT id, full_name, email FROM members WHERE status = 'active' ORDER BY full_name";
$members_result = mysqli_query($conn, $members_sql);

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إضافة دفعة جديدة</h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="member_id">العضو: *</label>
            <select id="member_id" name="member_id" required onchange="loadMemberSubscriptions(this.value)">
                <option value="">اختر العضو</option>
                <?php while ($member = mysqli_fetch_assoc($members_result)): ?>
                    <option value="<?php echo $member['id']; ?>" <?php echo (isset($_POST['member_id']) && $_POST['member_id'] == $member['id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($member['full_name']) . ' (' . htmlspecialchars($member['email']) . ')'; ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>

        <div class="form-group">
            <label for="subscription_id">الاشتراك المرتبط (اختياري):</label>
            <select id="subscription_id" name="subscription_id">
                <option value="">لا يوجد اشتراك مرتبط</option>
            </select>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                اختر اشتراكاً إذا كانت هذه الدفعة مرتبطة باشتراك معين
            </small>
        </div>

        <div class="form-group">
            <label for="amount">المبلغ (ر.س): *</label>
            <input type="number" id="amount" name="amount" min="0" step="0.01" value="<?php echo isset($_POST['amount']) ? $_POST['amount'] : ''; ?>" required>
        </div>

        <div class="form-group">
            <label for="payment_method">طريقة الدفع: *</label>
            <select id="payment_method" name="payment_method" required>
                <option value="">اختر طريقة الدفع</option>
                <option value="cash" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'cash') ? 'selected' : ''; ?>>نقداً</option>
                <option value="card" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'card') ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                <option value="bank_transfer" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>تحويل بنكي</option>
                <option value="online" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] == 'online') ? 'selected' : ''; ?>>دفع إلكتروني</option>
            </select>
        </div>

        <div class="form-group">
            <label for="payment_date">تاريخ الدفع: *</label>
            <input type="date" id="payment_date" name="payment_date" value="<?php echo isset($_POST['payment_date']) ? $_POST['payment_date'] : date('Y-m-d'); ?>" required>
        </div>

        <div class="form-group">
            <label for="status">حالة الدفعة: *</label>
            <select id="status" name="status" required>
                <option value="completed" <?php echo (isset($_POST['status']) && $_POST['status'] == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                <option value="pending" <?php echo (isset($_POST['status']) && $_POST['status'] == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                <option value="failed" <?php echo (isset($_POST['status']) && $_POST['status'] == 'failed') ? 'selected' : ''; ?>>فاشلة</option>
                <option value="refunded" <?php echo (isset($_POST['status']) && $_POST['status'] == 'refunded') ? 'selected' : ''; ?>>مرتدة</option>
            </select>
        </div>

        <div class="form-group">
            <label for="notes">ملاحظات:</label>
            <textarea id="notes" name="notes" rows="3"><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> إضافة الدفعة</button>
    </form>
</div>

<script>
function loadMemberSubscriptions(memberId) {
    const subscriptionSelect = document.getElementById('subscription_id');
    
    // مسح الخيارات الحالية
    subscriptionSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    
    if (!memberId) {
        subscriptionSelect.innerHTML = '<option value="">لا يوجد اشتراك مرتبط</option>';
        return;
    }
    
    // طلب AJAX لجلب اشتراكات العضو
    fetch(`../../api/get_member_subscriptions.php?member_id=${memberId}`)
        .then(response => response.json())
        .then(data => {
            subscriptionSelect.innerHTML = '<option value="">لا يوجد اشتراك مرتبط</option>';
            
            if (data.success && data.subscriptions.length > 0) {
                data.subscriptions.forEach(subscription => {
                    const option = document.createElement('option');
                    option.value = subscription.id;
                    option.textContent = `${subscription.plan_name} (${subscription.start_date} - ${subscription.end_date})`;
                    subscriptionSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            subscriptionSelect.innerHTML = '<option value="">خطأ في تحميل البيانات</option>';
        });
}
</script>

<?php include_once '../../includes/footer.php'; ?>
