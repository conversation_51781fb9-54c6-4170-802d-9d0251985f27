<?php
// ملف تشخيص بسيط جداً
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تشخيص النظام</h1>";

// 1. اختبار PHP
echo "<h2>1. معلومات PHP:</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "المجلد الحالي: " . __DIR__ . "<br>";
echo "الملف الحالي: " . __FILE__ . "<br>";

// 2. اختبار الملفات
echo "<h2>2. اختبار وجود الملفات:</h2>";
$files_to_check = [
    'includes/config.php',
    'includes/functions.php',
    'includes/auth.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// 3. اختبار تضمين config.php
echo "<h2>3. اختبار تضمين config.php:</h2>";
try {
    if (file_exists('includes/config.php')) {
        include_once 'includes/config.php';
        echo "✅ تم تضمين config.php بنجاح<br>";
        
        // اختبار المتغيرات
        if (defined('DB_SERVER')) {
            echo "✅ DB_SERVER: " . DB_SERVER . "<br>";
        } else {
            echo "❌ DB_SERVER غير معرف<br>";
        }
        
        if (defined('DB_NAME')) {
            echo "✅ DB_NAME: " . DB_NAME . "<br>";
        } else {
            echo "❌ DB_NAME غير معرف<br>";
        }
        
        // اختبار الاتصال
        if (isset($conn)) {
            if ($conn) {
                echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
                echo "إصدار MySQL: " . mysqli_get_server_info($conn) . "<br>";
            } else {
                echo "❌ فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error() . "<br>";
            }
        } else {
            echo "❌ متغير الاتصال غير موجود<br>";
        }
        
    } else {
        echo "❌ ملف config.php غير موجود<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في تضمين config.php: " . $e->getMessage() . "<br>";
}

// 4. اختبار الجلسة
echo "<h2>4. اختبار الجلسة:</h2>";
try {
    session_start();
    echo "✅ تم بدء الجلسة بنجاح<br>";
    echo "معرف الجلسة: " . session_id() . "<br>";
    
    if (isset($_SESSION['user_id'])) {
        echo "✅ المستخدم مسجل الدخول - ID: " . $_SESSION['user_id'] . "<br>";
    } else {
        echo "⚠️ المستخدم غير مسجل الدخول<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الجلسة: " . $e->getMessage() . "<br>";
}

// 5. اختبار قاعدة البيانات
if (isset($conn) && $conn) {
    echo "<h2>5. اختبار قاعدة البيانات:</h2>";
    
    $tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];
    foreach ($tables as $table) {
        $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if ($result && mysqli_num_rows($result) > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "❌ جدول $table غير موجود<br>";
        }
    }
}

// 6. اختبار إنشاء ملف بسيط
echo "<h2>6. اختبار الكتابة:</h2>";
try {
    $test_content = "اختبار الكتابة - " . date('Y-m-d H:i:s');
    if (file_put_contents('test_write.txt', $test_content)) {
        echo "✅ يمكن الكتابة في المجلد<br>";
        unlink('test_write.txt'); // حذف الملف التجريبي
    } else {
        echo "❌ لا يمكن الكتابة في المجلد<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار الكتابة: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>الخلاصة:</h2>";
echo "<p>إذا ظهرت علامات ❌ أعلاه، فهذه هي المشاكل التي تحتاج لحل.</p>";
echo "<p><a href='index.php'>محاولة الذهاب لصفحة تسجيل الدخول</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h1, h2 { color: #333; }
</style>
