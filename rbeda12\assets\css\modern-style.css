/* تصميم حديث وإبداعي لنادي أفانتي */

:root {
    /* ألوان النادي الليبي */
    --primary-color: #2E8B57; /* أخضر ليبي */
    --secondary-color: #DC143C; /* أحمر ليبي */
    --accent-color: #FFD700; /* ذهبي */
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --gray-color: #6c757d;
    
    /* تدرجات جميلة */
    --gradient-primary: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
    --gradient-secondary: linear-gradient(135deg, #DC143C 0%, #B22222 100%);
    --gradient-gold: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    /* ظلال */
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    
    /* انتقالات */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* إعادة تعيين عام */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
}

/* تحسين الهيدر */
.header {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    color: white;
    font-size: 1.8rem;
    font-weight: bold;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo::before {
    content: "🏋️";
    font-size: 2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 0;
}

.nav-links li a {
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.nav-links li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transition: var(--transition-normal);
    z-index: -1;
}

.nav-links li a:hover::before {
    left: 0;
}

.nav-links li a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* تحسين البطاقات */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.card .card-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 3rem;
    opacity: 0.1;
    color: var(--primary-color);
}

.card h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.card .number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card p {
    color: var(--gray-color);
    font-size: 0.9rem;
}

.card-footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 0.8rem;
    color: var(--gray-color);
}

/* تحسين الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transition: var(--transition-fast);
    transform: translate(-50%, -50%);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
}

.btn-success {
    background: var(--gradient-gold);
    color: var(--dark-color);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

/* تحسين الجداول */
.table-container {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    margin: 2rem 0;
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background: var(--gradient-primary);
    color: white;
}

thead th {
    padding: 1.2rem;
    text-align: right;
    font-weight: 600;
    font-size: 0.9rem;
}

tbody tr {
    transition: var(--transition-fast);
    border-bottom: 1px solid #f0f0f0;
}

tbody tr:hover {
    background: linear-gradient(90deg, rgba(46, 139, 87, 0.05) 0%, rgba(46, 139, 87, 0.02) 100%);
    transform: scale(1.01);
}

tbody td {
    padding: 1rem 1.2rem;
    font-size: 0.9rem;
}

/* تحسين النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 0.9rem;
    transition: var(--transition-normal);
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46, 139, 87, 0.1);
    transform: translateY(-2px);
}

/* تحسين الرسائل */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin: 1rem 0;
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.alert-error {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: #856404;
}

/* تحسين الصفحات */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-light);
}

.page-header h2 {
    color: var(--secondary-color);
    font-size: 1.8rem;
    font-weight: 600;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .card .number {
        font-size: 2rem;
    }
}

/* تأثيرات خاصة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسين شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}
