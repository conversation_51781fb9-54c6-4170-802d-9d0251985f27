<?php
$page_title = "تعديل العضو";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// التحقق من وجود معرف العضو
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف العضو غير صحيح';
    redirect('index.php');
    exit;
}

$member_id = (int)$_GET['id'];

// جلب بيانات العضو
$sql = "SELECT * FROM members WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $member_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'العضو غير موجود';
    redirect('index.php');
    exit;
}

$member = $result->fetch_assoc();
$stmt->close();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone_number']);
    $status = sanitize_input($_POST['status']);
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $join_date = sanitize_input($_POST['join_date']);
    $password = sanitize_input($_POST['password']);
    $confirm_password = sanitize_input($_POST['confirm_password']);

    // التحقق من صحة البيانات
    if (empty($name) || empty($email) || empty($join_date)) {
        $error = 'الحقول المطلوبة يجب تعبئتها';
    } elseif (!empty($password) && $password !== $confirm_password) {
        $error = 'كلمتا المرور غير متطابقتين';
    } elseif (!empty($password) && strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء العضو الحالي)
        $check_sql = "SELECT id FROM members WHERE email = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $email, $member_id);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'البريد الإلكتروني مسجل لعضو آخر';
        } else {
            // تحديث بيانات العضو
            if (!empty($password)) {
                // تحديث مع كلمة المرور
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $update_sql = "UPDATE members SET full_name = ?, email = ?, password = ?, phone_number = ?, join_date = ?, status = ?, is_admin = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("ssssssii", $name, $email, $hashed_password, $phone, $join_date, $status, $is_admin, $member_id);
            } else {
                // تحديث بدون كلمة المرور
                $update_sql = "UPDATE members SET full_name = ?, email = ?, phone_number = ?, join_date = ?, status = ?, is_admin = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("sssssii", $name, $email, $phone, $join_date, $status, $is_admin, $member_id);
            }

            if ($update_stmt->execute()) {
                $_SESSION['success'] = 'تم تحديث بيانات العضو بنجاح';

                // إذا كان العضو المحدث هو المستخدم الحالي، تحديث بيانات الجلسة
                if ($member_id == $_SESSION['user_id']) {
                    $_SESSION['user_name'] = $name;
                    $_SESSION['user_email'] = $email;
                    $_SESSION['is_admin'] = $is_admin;
                }

                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء تحديث البيانات: ' . $conn->error;
            }
            $update_stmt->close();
        }
        $check_stmt->close();
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>تعديل العضو: <?php echo htmlspecialchars($member['full_name']); ?></h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="full_name">الاسم الكامل: *</label>
            <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($member['full_name']); ?>" required>
        </div>

        <div class="form-group">
            <label for="email">البريد الإلكتروني: *</label>
            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($member['email']); ?>" required>
        </div>

        <div class="form-group">
            <label for="phone_number">رقم الهاتف:</label>
            <input type="tel" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($member['phone_number']); ?>">
        </div>

        <div class="form-group">
            <label for="join_date">تاريخ الانضمام: *</label>
            <input type="date" id="join_date" name="join_date" value="<?php echo $member['join_date']; ?>" required>
        </div>

        <div class="form-group">
            <label for="status">حالة العضو: *</label>
            <select id="status" name="status" required>
                <option value="active" <?php echo $member['status'] == 'active' ? 'selected' : ''; ?>>نشط</option>
                <option value="inactive" <?php echo $member['status'] == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                <option value="suspended" <?php echo $member['status'] == 'suspended' ? 'selected' : ''; ?>>موقوف</option>
            </select>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="is_admin" value="1" <?php echo $member['is_admin'] ? 'checked' : ''; ?>>
                منح صلاحيات المدير
            </label>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                تحذير: منح صلاحيات المدير يعطي العضو إمكانية الوصول الكامل للنظام
            </small>
        </div>

        <div class="form-group">
            <label for="password">كلمة المرور الجديدة (اتركها فارغة إذا لم ترغب في التغيير):</label>
            <input type="password" id="password" name="password">
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                يجب أن تكون 6 أحرف على الأقل إذا تم تعبئتها
            </small>
        </div>

        <div class="form-group">
            <label for="confirm_password">تأكيد كلمة المرور الجديدة:</label>
            <input type="password" id="confirm_password" name="confirm_password">
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> حفظ التغييرات</button>
    </form>
</div>

<?php include_once '../../includes/footer.php'; ?>