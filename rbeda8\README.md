# نظام إدارة نادي أفانتي الرياضي

نظام شامل لإدارة النادي الرياضي مكتوب بـ PHP مع قاعدة بيانات MySQL.

## المميزات

### للمديرين:
- لوحة تحكم شاملة مع الإحصائيات
- إدارة الأعضاء (إضافة، تعديل، حذف، تفعيل/إيقاف)
- إدارة خطط الاشتراك والأسعار
- إدارة المدفوعات والفواتير
- تقارير مفصلة

### للأعضاء:
- لوحة تحكم شخصية
- عرض حالة الاشتراك والمدة المتبقية
- تجديد الاشتراك أو تغيير الخطة
- إدارة الملف الشخصي
- تاريخ الاشتراكات والمدفوعات

## متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- قم بتشغيل الملف database.sql لإنشاء قاعدة البيانات والجداول
mysql -u username -p < database.sql
```

### 2. تحديث إعدادات قاعدة البيانات

قم بتحديث ملف `includes/config.php` بمعلومات قاعدة البيانات الخاصة بك:

```php
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'your_username');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'avantely_club_db');
```

### 3. رفع الملفات

قم برفع جميع الملفات إلى مجلد الويب الخاص بك.

### 4. تعيين الصلاحيات

تأكد من أن مجلد `error_log` قابل للكتابة:

```bash
chmod 755 error_log/
```

## بيانات الدخول الافتراضية

### المدير:
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password`

### عضو تجريبي:
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password`

## هيكل المشروع

```
rbeda2/
├── admin/                  # صفحات المدير
│   ├── dashboard.php      # لوحة تحكم المدير
│   ├── members/           # إدارة الأعضاء
│   ├── subscriptions/     # إدارة خطط الاشتراك
│   ├── payments/          # إدارة المدفوعات
│   └── reports/           # التقارير
├── member/                # صفحات الأعضاء
│   ├── dashboard.php      # لوحة تحكم العضو
│   ├── profile.php        # الملف الشخصي
│   └── subscriptions.php  # الاشتراكات
├── includes/              # الملفات المشتركة
│   ├── config.php         # إعدادات قاعدة البيانات
│   ├── functions.php      # الدوال المساعدة
│   ├── auth.php           # التحقق من الجلسة
│   ├── header.php         # رأس الصفحة
│   └── footer.php         # تذييل الصفحة
├── assets/                # الملفات الثابتة
│   ├── css/               # ملفات التنسيق
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور
├── api/                   # واجهات برمجة التطبيقات
├── index.php              # صفحة تسجيل الدخول
├── register.php           # صفحة التسجيل
├── logout.php             # تسجيل الخروج
└── database.sql           # ملف قاعدة البيانات
```

## قاعدة البيانات

### الجداول الرئيسية:

1. **members** - بيانات الأعضاء
2. **subscription_plans** - خطط الاشتراك
3. **subscriptions** - اشتراكات الأعضاء
4. **payments** - المدفوعات

## الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- استخدام Prepared Statements لمنع SQL Injection
- تنظيف المدخلات باستخدام `sanitize_input()`
- التحقق من الصلاحيات في كل صفحة

## التخصيص

### إضافة خطة اشتراك جديدة:
1. اذهب إلى لوحة تحكم المدير
2. اختر "إدارة خطط الاشتراك"
3. اضغط "إضافة خطة جديدة"

### تغيير التصميم:
قم بتعديل ملف `assets/css/style.css`

### إضافة مميزات جديدة:
- أضف الجداول المطلوبة في قاعدة البيانات
- أنشئ الصفحات في المجلد المناسب
- أضف الروابط في ملف `header.php`

## الدعم الفني

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور خصيصاً لنادي أفانتي الرياضي.
