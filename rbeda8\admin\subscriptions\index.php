<?php
$page_title = "إدارة خطط الاشتراك";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// معالجة حذف خطة الاشتراك
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $plan_id = (int)$_GET['delete'];

    // التحقق من عدم وجود اشتراكات نشطة لهذه الخطة
    $check_sql = "SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = ? AND status = 'active'";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $plan_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $active_subscriptions = $check_result->fetch_assoc()['count'];
    $check_stmt->close();

    if ($active_subscriptions > 0) {
        $_SESSION['error'] = "لا يمكن حذف هذه الخطة لوجود اشتراكات نشطة بها ($active_subscriptions اشتراك)";
    } else {
        $delete_sql = "DELETE FROM subscription_plans WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $plan_id);

        if ($delete_stmt->execute()) {
            $_SESSION['success'] = "تم حذف خطة الاشتراك بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء حذف خطة الاشتراك";
        }
        $delete_stmt->close();
    }

    redirect('index.php');
    exit;
}

// معالجة تغيير حالة الخطة
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $plan_id = (int)$_GET['toggle_status'];

    $current_status_sql = "SELECT is_active FROM subscription_plans WHERE id = ?";
    $current_stmt = $conn->prepare($current_status_sql);
    $current_stmt->bind_param("i", $plan_id);
    $current_stmt->execute();
    $current_result = $current_stmt->get_result();
    $current_plan = $current_result->fetch_assoc();

    if ($current_plan) {
        $new_status = $current_plan['is_active'] ? 0 : 1;

        $update_sql = "UPDATE subscription_plans SET is_active = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ii", $new_status, $plan_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث حالة خطة الاشتراك بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث حالة خطة الاشتراك";
        }
        $update_stmt->close();
    }
    $current_stmt->close();

    redirect('index.php');
    exit;
}

// جلب خطط الاشتراك
$sql = "SELECT sp.*,
               COUNT(s.id) as active_subscriptions
        FROM subscription_plans sp
        LEFT JOIN subscriptions s ON sp.id = s.plan_id AND s.status = 'active'
        GROUP BY sp.id
        ORDER BY sp.duration_months ASC";
$result = mysqli_query($conn, $sql);

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إدارة خطط الاشتراك</h2>
    <a href="add.php" class="btn"><i class="fas fa-plus"></i> إضافة خطة جديدة</a>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الرقم</th>
                <th>اسم الخطة</th>
                <th>المدة</th>
                <th>السعر الأساسي</th>
                <th>الخصم</th>
                <th>السعر النهائي</th>
                <th>الاشتراكات النشطة</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($result && mysqli_num_rows($result) > 0): ?>
                <?php while ($plan = mysqli_fetch_assoc($result)): ?>
                    <?php
                    $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
                    ?>
                    <tr>
                        <td><?php echo $plan['id']; ?></td>
                        <td><?php echo htmlspecialchars($plan['name']); ?></td>
                        <td>
                            <?php echo $plan['duration_months']; ?>
                            <?php echo $plan['duration_months'] == 1 ? 'شهر' : 'أشهر'; ?>
                        </td>
                        <td><?php echo number_format($plan['price'], 2); ?> ر.س</td>
                        <td>
                            <?php if ($plan['discount_percentage'] > 0): ?>
                                <span style="color: var(--success-color); font-weight: bold;">
                                    <?php echo $plan['discount_percentage']; ?>%
                                </span>
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong style="color: var(--primary-color);">
                                <?php echo number_format($final_price, 2); ?> ر.س
                            </strong>
                        </td>
                        <td>
                            <span class="status active" style="font-size: 14px;">
                                <?php echo $plan['active_subscriptions']; ?>
                            </span>
                        </td>
                        <td>
                            <span class="status <?php echo $plan['is_active'] ? 'active' : 'inactive'; ?>">
                                <?php echo $plan['is_active'] ? 'نشطة' : 'معطلة'; ?>
                            </span>
                        </td>
                        <td class="actions">
                            <a href="edit.php?id=<?php echo $plan['id']; ?>" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                <i class="fas fa-edit"></i> تعديل
                            </a>

                            <a href="?toggle_status=<?php echo $plan['id']; ?>"
                               class="btn <?php echo $plan['is_active'] ? 'btn-danger' : 'btn-success'; ?>"
                               style="padding: 5px 10px; font-size: 12px;"
                               onclick="return confirm('هل أنت متأكد من تغيير حالة هذه الخطة؟')">
                                <i class="fas fa-<?php echo $plan['is_active'] ? 'ban' : 'check'; ?>"></i>
                                <?php echo $plan['is_active'] ? 'تعطيل' : 'تفعيل'; ?>
                            </a>

                            <?php if ($plan['active_subscriptions'] == 0): ?>
                                <a href="?delete=<?php echo $plan['id']; ?>"
                                   class="btn btn-danger"
                                   style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من حذف هذه الخطة؟ هذا الإجراء لا يمكن التراجع عنه!')">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            <?php else: ?>
                                <span style="color: var(--gray-color); font-size: 12px; padding: 5px;">
                                    <i class="fas fa-lock"></i> محمية
                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="9" style="text-align: center; padding: 40px;">
                        <i class="fas fa-credit-card" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                        <p>لا توجد خطط اشتراك مضافة بعد</p>
                        <a href="add.php" class="btn" style="margin-top: 15px;">إضافة خطة جديدة</a>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php include_once '../../includes/footer.php'; ?>