<?php
// اختبار اتصال قاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار اتصال قاعدة البيانات</h1>";

// بيانات الاتصال
$server = 'localhost';
$username = 'avantely_club_db';
$password = 'L*(&PpsXn}g3Z$4n';
$database = 'avantely_club_db';

echo "<h2>بيانات الاتصال:</h2>";
echo "الخادم: $server<br>";
echo "اسم المستخدم: $username<br>";
echo "قاعدة البيانات: $database<br>";

// محاولة الاتصال
echo "<h2>محاولة الاتصال:</h2>";
$conn = mysqli_connect($server, $username, $password, $database);

if ($conn) {
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!<br>";
    echo "إصدار MySQL: " . mysqli_get_server_info($conn) . "<br>";
    
    // اختبار الجداول
    echo "<h2>اختبار الجداول:</h2>";
    $tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];
    
    foreach ($tables as $table) {
        $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if ($result && mysqli_num_rows($result) > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "❌ جدول $table غير موجود<br>";
        }
    }
    
    mysqli_close($conn);
} else {
    echo "❌ فشل الاتصال: " . mysqli_connect_error() . "<br>";
}

echo "<hr>";
echo "<p><a href='setup_database.php'>إعداد قاعدة البيانات</a></p>";
echo "<p><a href='index.php'>الذهاب لصفحة تسجيل الدخول</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h1, h2 { color: #333; }
</style>
