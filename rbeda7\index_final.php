<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// إذا كان المستخدم مسجل الدخول، وجهه للوحة التحكم
if (is_logged_in()) {
    if (is_admin()) {
        header("Location: admin/dashboard.php");
    } else {
        header("Location: member/dashboard.php");
    }
    exit;
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        // البحث عن المستخدم
        $email = mysqli_real_escape_string($conn, $email);
        $sql = "SELECT id, full_name, email, password, is_admin, status FROM members WHERE email = '$email'";
        $result = mysqli_query($conn, $sql);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            
            if (password_verify($password, $user['password'])) {
                if ($user['status'] === 'active') {
                    // تسجيل الدخول ناجح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['is_admin'] = (bool)$user['is_admin'];
                    
                    // توجيه المستخدم
                    if ($user['is_admin']) {
                        header("Location: admin/dashboard.php");
                    } else {
                        header("Location: member/dashboard.php");
                    }
                    exit;
                } else {
                    $error = 'حسابك غير نشط. يرجى التواصل مع الإدارة';
                }
            } else {
                $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
            }
        } else {
            $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}

$page_title = "تسجيل الدخول";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نادي أفانتي</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                <h1>نادي أفانتي</h1>
                <p>نظام إدارة النادي الرياضي</p>
            </div>
        </div>
        
        <div class="login-form">
            <?php if ($error): ?>
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <div class="input-group">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                               placeholder="أدخل بريدك الإلكتروني">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required 
                               placeholder="أدخل كلمة المرور">
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>
        </div>
        
        <div class="demo-accounts">
            <h3>حسابات تجريبية:</h3>
            <div class="demo-account">
                <strong>المدير:</strong>
                <span><EMAIL> / password</span>
            </div>
            <div class="demo-account">
                <strong>العضو:</strong>
                <span><EMAIL> / password</span>
            </div>
        </div>
        
        <div class="login-footer">
            <p>&copy; <?php echo date('Y'); ?> نادي أفانتي. جميع الحقوق محفوظة.</p>
        </div>
    </div>
    
    <script src="assets/js/script.js"></script>
</body>
</html>
