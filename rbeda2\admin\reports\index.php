<?php
$page_title = "التقارير والإحصائيات";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// معالجة تصدير التقارير
if (isset($_GET['export_report']) && in_array($_GET['export_report'], ['csv', 'pdf'])) {
    // التحقق من CSRF token
    if (!verify_csrf_token($_GET['csrf_token'] ?? '')) {
        $_SESSION['error'] = "رمز الأمان غير صحيح";
        redirect('index.php');
        exit;
    }

    // التحقق من معدل الطلبات
    if (!check_rate_limit('export_report', 2, 300)) {
        $_SESSION['error'] = "تم تجاوز الحد المسموح من عمليات التصدير";
        redirect('index.php');
        exit;
    }

    $export_type = $_GET['export_report'];
    $period = $_GET['period'] ?? 'month';

    // جلب بيانات التقرير للتصدير
    // سيتم إضافة منطق التصدير هنا
}

// فترة التقرير
$period = isset($_GET['period']) ? sanitize_input($_GET['period']) : 'month';
$custom_from = isset($_GET['custom_from']) ? sanitize_input($_GET['custom_from']) : '';
$custom_to = isset($_GET['custom_to']) ? sanitize_input($_GET['custom_to']) : '';

// تحديد التواريخ حسب الفترة
switch ($period) {
    case 'today':
        $date_from = date('Y-m-d');
        $date_to = date('Y-m-d');
        break;
    case 'week':
        $date_from = date('Y-m-d', strtotime('-7 days'));
        $date_to = date('Y-m-d');
        break;
    case 'month':
        $date_from = date('Y-m-01');
        $date_to = date('Y-m-t');
        break;
    case 'quarter':
        $quarter_start = date('Y-m-01', strtotime('-2 months'));
        $date_from = $quarter_start;
        $date_to = date('Y-m-t');
        break;
    case 'year':
        $date_from = date('Y-01-01');
        $date_to = date('Y-12-31');
        break;
    case 'custom':
        $date_from = $custom_from ?: date('Y-m-01');
        $date_to = $custom_to ?: date('Y-m-t');
        break;
    default:
        $date_from = date('Y-m-01');
        $date_to = date('Y-m-t');
}

// إحصائيات عامة
$general_stats_sql = "SELECT
    (SELECT COUNT(*) FROM members) as total_members,
    (SELECT COUNT(*) FROM members WHERE status = 'active') as active_members,
    (SELECT COUNT(*) FROM subscriptions WHERE status = 'active') as active_subscriptions,
    (SELECT COUNT(*) FROM subscription_plans WHERE is_active = 1) as active_plans";
$general_stats = mysqli_fetch_assoc(mysqli_query($conn, $general_stats_sql));

// إحصائيات المدفوعات للفترة المحددة
$payments_stats_sql = "SELECT
    COUNT(*) as total_payments,
    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
    SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
    SUM(CASE WHEN status = 'refunded' THEN amount ELSE 0 END) as refunded_amount,
    AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_payment
    FROM payments
    WHERE payment_date BETWEEN ? AND ?";
$payments_stmt = $conn->prepare($payments_stats_sql);
$payments_stmt->bind_param("ss", $date_from, $date_to);
$payments_stmt->execute();
$payments_stats = $payments_stmt->get_result()->fetch_assoc();
$payments_stmt->close();

// إحصائيات الاشتراكات للفترة المحددة
$subscriptions_stats_sql = "SELECT
    COUNT(*) as new_subscriptions,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_in_period,
    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_in_period
    FROM subscriptions
    WHERE start_date BETWEEN ? AND ?";
$subs_stmt = $conn->prepare($subscriptions_stats_sql);
$subs_stmt->bind_param("ss", $date_from, $date_to);
$subs_stmt->execute();
$subscriptions_stats = $subs_stmt->get_result()->fetch_assoc();
$subs_stmt->close();

// أفضل خطط الاشتراك
$top_plans_sql = "SELECT sp.name, COUNT(s.id) as subscription_count, SUM(s.amount_paid) as total_revenue
    FROM subscription_plans sp
    LEFT JOIN subscriptions s ON sp.id = s.plan_id AND s.start_date BETWEEN ? AND ?
    GROUP BY sp.id, sp.name
    ORDER BY subscription_count DESC, total_revenue DESC
    LIMIT 5";
$top_plans_stmt = $conn->prepare($top_plans_sql);
$top_plans_stmt->bind_param("ss", $date_from, $date_to);
$top_plans_stmt->execute();
$top_plans = $top_plans_stmt->get_result();
$top_plans_stmt->close();

// أحدث الأعضاء
$recent_members_sql = "SELECT full_name, email, join_date, status
    FROM members
    WHERE join_date BETWEEN ? AND ?
    ORDER BY join_date DESC
    LIMIT 10";
$recent_stmt = $conn->prepare($recent_members_sql);
$recent_stmt->bind_param("ss", $date_from, $date_to);
$recent_stmt->execute();
$recent_members = $recent_stmt->get_result();
$recent_stmt->close();

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>التقارير والإحصائيات</h2>
    <div style="display: flex; gap: 10px;">
        <a href="financial.php" class="btn"><i class="fas fa-chart-line"></i> التقرير المالي</a>
        <a href="members.php" class="btn"><i class="fas fa-users"></i> تقرير الأعضاء</a>
    </div>
</div>

<!-- فلتر الفترة الزمنية -->
<div class="table-container" style="margin-bottom: 30px;">
    <form method="GET" style="padding: 20px; display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
        <div class="form-group" style="margin-bottom: 0;">
            <label for="period">الفترة الزمنية:</label>
            <select id="period" name="period" onchange="toggleCustomDates()">
                <option value="today" <?php echo $period == 'today' ? 'selected' : ''; ?>>اليوم</option>
                <option value="week" <?php echo $period == 'week' ? 'selected' : ''; ?>>آخر 7 أيام</option>
                <option value="month" <?php echo $period == 'month' ? 'selected' : ''; ?>>هذا الشهر</option>
                <option value="quarter" <?php echo $period == 'quarter' ? 'selected' : ''; ?>>آخر 3 أشهر</option>
                <option value="year" <?php echo $period == 'year' ? 'selected' : ''; ?>>هذا العام</option>
                <option value="custom" <?php echo $period == 'custom' ? 'selected' : ''; ?>>فترة مخصصة</option>
            </select>
        </div>

        <div id="custom-dates" style="display: <?php echo $period == 'custom' ? 'flex' : 'none'; ?>; gap: 15px;">
            <div class="form-group" style="margin-bottom: 0;">
                <label for="custom_from">من:</label>
                <input type="date" id="custom_from" name="custom_from" value="<?php echo $custom_from; ?>">
            </div>
            <div class="form-group" style="margin-bottom: 0;">
                <label for="custom_to">إلى:</label>
                <input type="date" id="custom_to" name="custom_to" value="<?php echo $custom_to; ?>">
            </div>
        </div>

        <button type="submit" class="btn"><i class="fas fa-search"></i> عرض التقرير</button>
    </form>
</div>

<!-- الإحصائيات العامة -->
<div class="dashboard-header" style="margin-bottom: 30px;">
    <h3>نظرة عامة (<?php echo $date_from; ?> إلى <?php echo $date_to; ?>)</h3>
</div>

<div class="dashboard-cards" style="margin-bottom: 40px;">
    <div class="card">
        <h3>إجمالي الأعضاء</h3>
        <div class="number"><?php echo $general_stats['total_members']; ?></div>
        <p>الأعضاء النشطين: <?php echo $general_stats['active_members']; ?></p>
    </div>
    <div class="card">
        <h3>الإيرادات</h3>
        <div class="number" style="color: var(--success-color);"><?php echo number_format($payments_stats['total_revenue'], 2); ?> ر.س</div>
        <p>من <?php echo $payments_stats['total_payments']; ?> دفعة</p>
    </div>
    <div class="card">
        <h3>الاشتراكات الجديدة</h3>
        <div class="number"><?php echo $subscriptions_stats['new_subscriptions']; ?></div>
        <p>النشطة: <?php echo $subscriptions_stats['active_in_period']; ?></p>
    </div>
    <div class="card">
        <h3>متوسط الدفعة</h3>
        <div class="number"><?php echo number_format($payments_stats['avg_payment'] ?: 0, 2); ?> ر.س</div>
        <p>المعلقة: <?php echo number_format($payments_stats['pending_amount'], 2); ?> ر.س</p>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px;">
    <!-- أفضل خطط الاشتراك -->
    <div class="table-container">
        <h3 style="padding: 20px; margin: 0; border-bottom: 1px solid #eee;">أفضل خطط الاشتراك</h3>
        <table>
            <thead>
                <tr>
                    <th>اسم الخطة</th>
                    <th>عدد الاشتراكات</th>
                    <th>الإيرادات</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($top_plans->num_rows > 0): ?>
                    <?php while ($plan = $top_plans->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($plan['name']); ?></td>
                            <td><?php echo $plan['subscription_count']; ?></td>
                            <td><?php echo number_format($plan['total_revenue'], 2); ?> ر.س</td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="3" style="text-align: center; padding: 20px;">لا توجد بيانات للفترة المحددة</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- أحدث الأعضاء -->
    <div class="table-container">
        <h3 style="padding: 20px; margin: 0; border-bottom: 1px solid #eee;">الأعضاء الجدد</h3>
        <table>
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>تاريخ الانضمام</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($recent_members->num_rows > 0): ?>
                    <?php while ($member = $recent_members->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($member['full_name']); ?></strong><br>
                                <small style="color: var(--gray-color);"><?php echo htmlspecialchars($member['email']); ?></small>
                            </td>
                            <td><?php echo $member['join_date']; ?></td>
                            <td>
                                <span class="status <?php echo $member['status']; ?>">
                                    <?php
                                    switch($member['status']) {
                                        case 'active': echo 'نشط'; break;
                                        case 'inactive': echo 'غير نشط'; break;
                                        case 'suspended': echo 'موقوف'; break;
                                    }
                                    ?>
                                </span>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="3" style="text-align: center; padding: 20px;">لا توجد أعضاء جدد في هذه الفترة</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="dashboard-header" style="margin-bottom: 20px;">
    <h3>إحصائيات تفصيلية</h3>
</div>

<div class="dashboard-cards">
    <?php
    // إحصائيات طرق الدفع
    $payment_methods_sql = "SELECT payment_method, COUNT(*) as count, SUM(amount) as total
        FROM payments
        WHERE payment_date BETWEEN ? AND ? AND status = 'completed'
        GROUP BY payment_method";
    $methods_stmt = $conn->prepare($payment_methods_sql);
    $methods_stmt->bind_param("ss", $date_from, $date_to);
    $methods_stmt->execute();
    $payment_methods = $methods_stmt->get_result();
    $methods_stmt->close();
    ?>

    <?php while ($method = $payment_methods->fetch_assoc()): ?>
        <div class="card">
            <h3>
                <?php
                switch($method['payment_method']) {
                    case 'cash': echo '<i class="fas fa-money-bill"></i> نقداً'; break;
                    case 'card': echo '<i class="fas fa-credit-card"></i> بطاقة ائتمان'; break;
                    case 'bank_transfer': echo '<i class="fas fa-university"></i> تحويل بنكي'; break;
                    case 'online': echo '<i class="fas fa-globe"></i> دفع إلكتروني'; break;
                    default: echo $method['payment_method']; break;
                }
                ?>
            </h3>
            <div class="number"><?php echo number_format($method['total'], 2); ?> ر.س</div>
            <p>من <?php echo $method['count']; ?> دفعة</p>
        </div>
    <?php endwhile; ?>
</div>

<script>
function toggleCustomDates() {
    const period = document.getElementById('period').value;
    const customDates = document.getElementById('custom-dates');

    if (period === 'custom') {
        customDates.style.display = 'flex';
    } else {
        customDates.style.display = 'none';
    }
}

// إضافة رسوم بيانية تفاعلية
function initCharts() {
    // رسم بياني للإيرادات الشهرية
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات (ر.س)',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'الإيرادات الشهرية'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // رسم بياني دائري لحالات الأعضاء
    const membersCtx = document.getElementById('membersChart');
    if (membersCtx) {
        new Chart(membersCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'غير نشط', 'موقوف'],
                datasets: [{
                    data: [<?php
                        $active = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'active'"))['count'];
                        $inactive = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'inactive'"))['count'];
                        $suspended = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'suspended'"))['count'];
                        echo "$active, $inactive, $suspended";
                    ?>],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'توزيع حالات الأعضاء'
                    }
                }
            }
        });
    }
}

// تهيئة الرسوم البيانية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (typeof Chart !== 'undefined') {
        initCharts();
    }
});
</script>

<!-- إضافة مكتبة Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- إضافة قسم الرسوم البيانية -->
<div class="charts-section" style="margin-top: 30px;">
    <div class="page-header">
        <h2>الرسوم البيانية</h2>
        <div style="display: flex; gap: 10px;">
            <button class="btn btn-success" onclick="exportChart('revenue')">
                <i class="fas fa-download"></i> تصدير رسم الإيرادات
            </button>
            <button class="btn btn-info" onclick="exportChart('members')">
                <i class="fas fa-download"></i> تصدير رسم الأعضاء
            </button>
        </div>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
        <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <canvas id="revenueChart"></canvas>
        </div>
        <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <canvas id="membersChart"></canvas>
        </div>
    </div>
</div>

<script>
function exportChart(chartType) {
    // منطق تصدير الرسوم البيانية
    alert('ميزة تصدير الرسوم البيانية ستكون متاحة قريباً');
}
</script>

<style>
.charts-section {
    margin-top: 30px;
}

.chart-container {
    position: relative;
    height: 400px;
}

@media (max-width: 768px) {
    .charts-section > div[style*="grid"] {
        grid-template-columns: 1fr !important;
    }
}
</style>

<?php include_once '../../includes/footer.php'; ?>