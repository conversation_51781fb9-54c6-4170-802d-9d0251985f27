<?php
// تضمين الدوال المطلوبة
if (!function_exists('is_logged_in')) {
    require_once dirname(__FILE__) . '/functions.php';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>نادي أفانتي</title>
    <link rel="stylesheet" href="<?php echo isset($css_path) ? $css_path : '../assets/css/'; ?>style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي
            </div>
            <ul class="nav-links">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/members/"><i class="fas fa-users"></i> الأعضاء</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/subscriptions/"><i class="fas fa-credit-card"></i> الاشتراكات</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/payments/"><i class="fas fa-money-bill"></i> المدفوعات</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/reports/"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/logs/"><i class="fas fa-file-alt"></i> السجلات</a></li>
                    <?php else: ?>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/subscriptions.php"><i class="fas fa-credit-card"></i> اشتراكاتي</a></li>
                    <?php endif; ?>
                    <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <li><span style="color: #fff; margin-right: 15px;"><i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?></span></li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>

    <div class="container">
        <?php if (isset($_SESSION['error'])): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>
