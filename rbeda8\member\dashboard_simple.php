<?php
// نسخة مبسطة من لوحة تحكم العضو
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: ../index.php");
    exit;
}

// تضمين ملف الإعدادات
require_once '../includes/config.php';

$user_id = $_SESSION['user_id'];
$page_title = "لوحة تحكم العضو";

// جلب بيانات العضو
$user_sql = "SELECT * FROM members WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_sql);
$user = mysqli_fetch_assoc($user_result);

if (!$user) {
    echo "خطأ: لم يتم العثور على بيانات العضو";
    exit;
}

// جلب الاشتراك النشط
$subscription_sql = "SELECT s.*, sp.name as plan_name 
                    FROM subscriptions s 
                    LEFT JOIN subscription_plans sp ON s.plan_id = sp.id 
                    WHERE s.member_id = $user_id AND s.status = 'active' 
                    ORDER BY s.end_date DESC LIMIT 1";
$subscription_result = mysqli_query($conn, $subscription_sql);
$current_subscription = mysqli_fetch_assoc($subscription_result);

// جلب آخر دفعة
$payment_sql = "SELECT * FROM payments WHERE member_id = $user_id ORDER BY payment_date DESC LIMIT 1";
$payment_result = mysqli_query($conn, $payment_sql);
$last_payment = mysqli_fetch_assoc($payment_result);

// حساب الأيام المتبقية
$days_remaining = 0;
if ($current_subscription) {
    $end_date = new DateTime($current_subscription['end_date']);
    $today = new DateTime();
    $interval = $today->diff($end_date);
    $days_remaining = $interval->days;
    if ($today > $end_date) {
        $days_remaining = -$days_remaining;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نادي أفانتي</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي
            </div>
            <ul class="nav-links">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                <li><a href="subscriptions.php"><i class="fas fa-credit-card"></i> اشتراكاتي</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                <li><span style="color: #fff; margin-right: 15px;"><i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($user['full_name']); ?></span></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <?php if (isset($_SESSION['error'])): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <div class="dashboard-header">
            <h1>مرحباً بك، <?php echo htmlspecialchars($user['full_name']); ?></h1>
            <p>هنا يمكنك إدارة اشتراكاتك وملفك الشخصي.</p>
        </div>

        <div class="dashboard-cards">
            <div class="card">
                <h3>حالة الاشتراك</h3>
                <?php if ($current_subscription): ?>
                    <?php 
                    $status_class = 'active';
                    $status_text = 'نشط';
                    if ($days_remaining < 0) {
                        $status_class = 'inactive';
                        $status_text = 'منتهي';
                    } elseif ($days_remaining <= 7) {
                        $status_class = 'suspended';
                        $status_text = 'ينتهي قريباً';
                    }
                    ?>
                    <div class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></div>
                    <p>ينتهي في: <?php echo $current_subscription['end_date']; ?></p>
                <?php else: ?>
                    <div class="status inactive">لا يوجد اشتراك</div>
                    <p><a href="subscriptions.php" class="btn" style="margin-top: 10px;">اشترك الآن</a></p>
                <?php endif; ?>
            </div>
            
            <div class="card">
                <h3>آخر دفعة</h3>
                <?php if ($last_payment): ?>
                    <div class="number"><?php echo number_format($last_payment['amount'], 2); ?> ر.س</div>
                    <p>بتاريخ: <?php echo $last_payment['payment_date']; ?></p>
                <?php else: ?>
                    <div class="number">-</div>
                    <p>لا توجد دفعات</p>
                <?php endif; ?>
            </div>
            
            <div class="card">
                <h3>المدة المتبقية</h3>
                <?php if ($current_subscription && $days_remaining >= 0): ?>
                    <div class="number"><?php echo $days_remaining; ?> يوم</div>
                <?php elseif ($current_subscription && $days_remaining < 0): ?>
                    <div class="number" style="color: var(--error-color);">منتهي</div>
                <?php else: ?>
                    <div class="number">-</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="page-header">
            <h2>الاشتراكات الخاصة بي</h2>
            <a href="subscriptions.php" class="btn">عرض الكل</a>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>نوع الاشتراك</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الانتهاء</th>
                        <th>المبلغ المدفوع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // جلب آخر 3 اشتراكات للعضو
                    $subscriptions_sql = "SELECT s.*, sp.name as plan_name 
                                         FROM subscriptions s 
                                         LEFT JOIN subscription_plans sp ON s.plan_id = sp.id 
                                         WHERE s.member_id = $user_id 
                                         ORDER BY s.created_at DESC 
                                         LIMIT 3";
                    $subscriptions_result = mysqli_query($conn, $subscriptions_sql);
                    
                    if ($subscriptions_result && mysqli_num_rows($subscriptions_result) > 0):
                        while ($subscription = mysqli_fetch_assoc($subscriptions_result)):
                            $status_class = $subscription['status'];
                            $status_text = '';
                            switch($subscription['status']) {
                                case 'active': $status_text = 'نشط'; break;
                                case 'expired': $status_text = 'منتهي'; break;
                                case 'cancelled': $status_text = 'ملغي'; break;
                            }
                    ?>
                        <tr>
                            <td><?php echo htmlspecialchars($subscription['plan_name'] ?: 'غير محدد'); ?></td>
                            <td><?php echo $subscription['start_date']; ?></td>
                            <td><?php echo $subscription['end_date']; ?></td>
                            <td><?php echo number_format($subscription['amount_paid'], 2); ?> ر.س</td>
                            <td><span class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                            <td class="actions">
                                <?php if ($subscription['status'] == 'active'): ?>
                                    <span style="color: var(--success-color); font-size: 12px;">
                                        <i class="fas fa-check-circle"></i> ساري
                                    </span>
                                <?php else: ?>
                                    <a href="subscriptions.php" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                        <i class="fas fa-plus"></i> تجديد
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php 
                        endwhile;
                    else: 
                    ?>
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 40px;">
                                <i class="fas fa-credit-card" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                                <p>لا توجد اشتراكات بعد</p>
                                <a href="subscriptions.php" class="btn" style="margin-top: 15px;">
                                    <i class="fas fa-plus"></i> اشترك الآن
                                </a>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- إشعارات مهمة -->
        <?php if ($current_subscription && $days_remaining <= 7 && $days_remaining >= 0): ?>
            <div class="warning" style="margin-top: 30px;">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تنبيه:</strong> اشتراكك سينتهي خلال <?php echo $days_remaining; ?> أيام. 
                <a href="subscriptions.php" style="color: var(--warning-color); text-decoration: underline;">جدد اشتراكك الآن</a>
            </div>
        <?php elseif ($current_subscription && $days_remaining < 0): ?>
            <div class="error" style="margin-top: 30px;">
                <i class="fas fa-times-circle"></i>
                <strong>انتهى الاشتراك:</strong> اشتراكك منتهي منذ <?php echo abs($days_remaining); ?> يوم. 
                <a href="subscriptions.php" style="color: var(--error-color); text-decoration: underline;">جدد اشتراكك الآن</a>
            </div>
        <?php endif; ?>

    </div>

    <footer style="background-color: var(--secondary-color); color: white; text-align: center; padding: 20px 0; margin-top: 50px;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> نادي أفانتي. جميع الحقوق محفوظة.</p>
            <p>
                <i class="fas fa-phone"></i> +966 50 123 4567 | 
                <i class="fas fa-envelope"></i> <EMAIL> |
                <i class="fas fa-map-marker-alt"></i> الرياض، المملكة العربية السعودية
            </p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
</body>
</html>
