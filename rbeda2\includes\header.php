<?php
// تضمين الدوال المطلوبة
if (!function_exists('is_logged_in')) {
    require_once dirname(__FILE__) . '/functions.php';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>نادي أفانتي</title>
    <link rel="stylesheet" href="<?php echo isset($css_path) ? $css_path : '../assets/css/'; ?>style.css">
    <link rel="stylesheet" href="<?php echo isset($css_path) ? $css_path : '../assets/css/'; ?>modern-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="container">
            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>index.php" class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي الليبي
            </a>
            <ul class="nav-links">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/members/"><i class="fas fa-users"></i> الأعضاء</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/subscriptions/"><i class="fas fa-credit-card"></i> الاشتراكات</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/payments/"><i class="fas fa-money-bill"></i> المدفوعات</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/reports/"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/logs/"><i class="fas fa-file-alt"></i> السجلات</a></li>
                    <?php else: ?>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/subscriptions.php"><i class="fas fa-credit-card"></i> اشتراكاتي</a></li>
                    <?php endif; ?>
                    <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <li><span style="color: #fff; margin-right: 15px;"><i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?></span></li>
                <?php endif; ?>
            </ul>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-error fade-in-up">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></span>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success fade-in-up">
                    <i class="fas fa-check-circle"></i>
                    <span><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></span>
                </div>
            <?php endif; ?>
