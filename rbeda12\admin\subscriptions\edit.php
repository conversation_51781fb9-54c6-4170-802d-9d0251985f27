<?php
$page_title = "تعديل خطة الاشتراك";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// التحقق من وجود معرف الخطة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف خطة الاشتراك غير صحيح';
    redirect('index.php');
    exit;
}

$plan_id = (int)$_GET['id'];

// جلب بيانات خطة الاشتراك
$sql = "SELECT * FROM subscription_plans WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $plan_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'خطة الاشتراك غير موجودة';
    redirect('index.php');
    exit;
}

$plan = $result->fetch_assoc();
$stmt->close();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name']);
    $description = sanitize_input($_POST['description']);
    $duration_months = (int)$_POST['duration_months'];
    $price = (float)$_POST['price'];
    $discount_percentage = (float)$_POST['discount_percentage'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // التحقق من صحة البيانات
    if (empty($name) || $duration_months <= 0 || $price <= 0) {
        $error = 'جميع الحقول المطلوبة يجب تعبئتها بقيم صحيحة';
    } elseif ($discount_percentage < 0 || $discount_percentage > 100) {
        $error = 'نسبة الخصم يجب أن تكون بين 0 و 100';
    } else {
        // التحقق من عدم وجود خطة بنفس الاسم (باستثناء الخطة الحالية)
        $check_sql = "SELECT id FROM subscription_plans WHERE name = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $name, $plan_id);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'يوجد خطة اشتراك أخرى بنفس هذا الاسم';
        } else {
            // تحديث خطة الاشتراك
            $update_sql = "UPDATE subscription_plans SET name = ?, description = ?, duration_months = ?, price = ?, discount_percentage = ?, is_active = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ssiddii", $name, $description, $duration_months, $price, $discount_percentage, $is_active, $plan_id);

            if ($update_stmt->execute()) {
                $_SESSION['success'] = 'تم تحديث خطة الاشتراك بنجاح';
                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء تحديث خطة الاشتراك: ' . $conn->error;
            }
            $update_stmt->close();
        }
        $check_stmt->close();
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>تعديل خطة الاشتراك: <?php echo htmlspecialchars($plan['name']); ?></h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="form-group">
            <label for="name">اسم خطة الاشتراك: *</label>
            <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($plan['name']); ?>" required>
        </div>

        <div class="form-group">
            <label for="description">وصف الخطة:</label>
            <textarea id="description" name="description" rows="3"><?php echo htmlspecialchars($plan['description']); ?></textarea>
        </div>

        <div class="form-group">
            <label for="duration_months">مدة الاشتراك (بالأشهر): *</label>
            <input type="number" id="duration_months" name="duration_months" min="1" max="60" value="<?php echo $plan['duration_months']; ?>" required>
        </div>

        <div class="form-group">
            <label for="price">السعر الأساسي (ر.س): *</label>
            <input type="number" id="price" name="price" min="0" step="0.01" value="<?php echo $plan['price']; ?>" required>
        </div>

        <div class="form-group">
            <label for="discount_percentage">نسبة الخصم (%):</label>
            <input type="number" id="discount_percentage" name="discount_percentage" min="0" max="100" step="0.01" value="<?php echo $plan['discount_percentage']; ?>">
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" value="1" <?php echo $plan['is_active'] ? 'checked' : ''; ?>>
                خطة نشطة ومتاحة للاشتراك
            </label>
        </div>

        <!-- معاينة السعر النهائي -->
        <div class="form-group" style="background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
            <h4 style="margin-bottom: 10px; color: var(--secondary-color);">معاينة السعر:</h4>
            <div id="price-preview">
                <p><strong>السعر الأساسي:</strong> <span id="base-price">0.00</span> ر.س</p>
                <p><strong>الخصم:</strong> <span id="discount-amount">0.00</span> ر.س (<span id="discount-percent">0</span>%)</p>
                <p style="font-size: 18px; color: var(--primary-color);"><strong>السعر النهائي:</strong> <span id="final-price">0.00</span> ر.س</p>
            </div>
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> حفظ التغييرات</button>
    </form>
</div>

<script>
// تحديث معاينة السعر عند تغيير القيم
function updatePricePreview() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const discountAmount = price * (discountPercent / 100);
    const finalPrice = price - discountAmount;
    
    document.getElementById('base-price').textContent = price.toFixed(2);
    document.getElementById('discount-amount').textContent = discountAmount.toFixed(2);
    document.getElementById('discount-percent').textContent = discountPercent.toFixed(1);
    document.getElementById('final-price').textContent = finalPrice.toFixed(2);
}

document.getElementById('price').addEventListener('input', updatePricePreview);
document.getElementById('discount_percentage').addEventListener('input', updatePricePreview);

// تحديث المعاينة عند تحميل الصفحة
updatePricePreview();
</script>

<?php include_once '../../includes/footer.php'; ?>
