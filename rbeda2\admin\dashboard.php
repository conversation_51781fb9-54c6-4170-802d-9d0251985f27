<?php
session_start();

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php");
    exit;
}

$page_title = "لوحة التحكم";
require_once '../includes/config.php';
require_once '../includes/functions.php';

// تهيئة المتغيرات بقيم افتراضية
$members_count = 0;
$active_members = 0;
$revenue = 0;
$monthly_revenue = 0;
$active_subscriptions = 0;
$expiring_subscriptions = 0;

// استعلامات للحصول على الإحصائيات مع معالجة الأخطاء
try {
    // عدد الأعضاء
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM members");
    if ($result) {
        $members_count = mysqli_fetch_assoc($result)['count'];
    }

    // الأعضاء النشطين
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'active'");
    if ($result) {
        $active_members = mysqli_fetch_assoc($result)['count'];
    }

    // إجمالي الإيرادات
    $result = mysqli_query($conn, "SELECT SUM(amount) as total FROM payments WHERE status = 'completed'");
    if ($result) {
        $data = mysqli_fetch_assoc($result);
        $revenue = $data['total'] ?? 0;
    }

    // الاشتراكات المنتهية (تنتهي خلال 7 أيام)
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active' AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)");
    if ($result) {
        $expiring_subscriptions = mysqli_fetch_assoc($result)['count'];
    }

    // الإيرادات الشهرية
    $result = mysqli_query($conn, "SELECT SUM(amount) as total FROM payments WHERE status = 'completed' AND MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE())");
    if ($result) {
        $data = mysqli_fetch_assoc($result);
        $monthly_revenue = $data['total'] ?? 0;
    }

    // الاشتراكات النشطة
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'");
    if ($result) {
        $active_subscriptions = mysqli_fetch_assoc($result)['count'];
    }

} catch (Exception $e) {
    // في حالة الخطأ، استخدم القيم الافتراضية
    error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
}

$nav_path = '../';
include_once '../includes/header.php';
?>

<div class="dashboard-header">
    <h1>مرحباً بك، <?php echo htmlspecialchars($_SESSION['user_name']); ?></h1>
    <p>هنا يمكنك إدارة النادي والتحكم في الأعضاء والاشتراكات.</p>
</div>

<div class="dashboard-cards">
    <div class="card">
        <h3>إجمالي الأعضاء</h3>
        <div class="number"><?php echo $members_count; ?></div>
        <p>النشطين: <?php echo $active_members; ?></p>
    </div>
    <div class="card">
        <h3>الإيرادات الشهرية</h3>
        <div class="number" style="color: var(--success-color);"><?php echo number_format($monthly_revenue, 2); ?> ر.س</div>
        <p>الإجمالي: <?php echo number_format($revenue, 2); ?> ر.س</p>
    </div>
    <div class="card">
        <h3>الاشتراكات النشطة</h3>
        <div class="number"><?php echo $active_subscriptions; ?></div>
        <p>تنتهي قريباً: <?php echo $expiring_subscriptions; ?></p>
    </div>
    <div class="card">
        <h3>الاشتراكات المنتهية قريباً</h3>
        <div class="number" style="color: var(--warning-color);"><?php echo $expiring_subscriptions; ?></div>
        <p>خلال 7 أيام</p>
    </div>
</div>

<div class="page-header">
    <h2>آخر الأعضاء المسجلين</h2>
    <a href="members/" class="btn">عرض الكل</a>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الهاتف</th>
                <th>تاريخ الانضمام</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $sql = "SELECT * FROM members ORDER BY join_date DESC LIMIT 5";
            $result = mysqli_query($conn, $sql);

            if ($result && mysqli_num_rows($result) > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    echo "<tr>
                        <td>" . htmlspecialchars($row['full_name']) . "</td>
                        <td>" . htmlspecialchars($row['email']) . "</td>
                        <td>" . htmlspecialchars($row['phone_number'] ?? '') . "</td>
                        <td>{$row['join_date']}</td>
                        <td><span class='status {$row['status']}'>";

                    if ($row['status'] == 'active') echo 'نشط';
                    elseif ($row['status'] == 'inactive') echo 'غير نشط';
                    else echo 'موقوف';

                    echo "</span></td>
                    </tr>";
                }
            } else {
                echo "<tr><td colspan='5' style='text-align: center; padding: 20px;'>لا توجد بيانات</td></tr>";
            }
            ?>
        </tbody>
    </table>
</div>

<?php include_once '../includes/footer.php'; ?>