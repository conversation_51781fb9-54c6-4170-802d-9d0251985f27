<?php
// فحص الجداول المطلوبة
require_once 'includes/config.php';

echo "<h1>فحص الجداول</h1>";

$required_tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];

foreach ($required_tables as $table) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
    if ($result && mysqli_num_rows($result) > 0) {
        echo "✅ جدول $table موجود<br>";
        
        // عدد السجلات
        $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = mysqli_fetch_assoc($count_result)['count'];
            echo "&nbsp;&nbsp;&nbsp;📊 عدد السجلات: $count<br>";
        }
    } else {
        echo "❌ جدول $table غير موجود<br>";
    }
}

echo "<hr>";
echo "<p><a href='setup_database.php'>إعداد قاعدة البيانات</a></p>";
echo "<p><a href='admin/dashboard.php'>لوحة تحكم المدير</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
</style>
