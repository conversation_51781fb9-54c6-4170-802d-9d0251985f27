<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// إذا كان المستخدم مسجل الدخول بالفعل، توجيهه للوحة التحكم
if (is_logged_in()) {
    if (is_admin()) {
        header("Location: admin/dashboard.php");
    } else {
        header("Location: member/dashboard.php");
    }
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email']);
    $password = sanitize_input($_POST['password']);

    // التحقق من صحة البيانات
    if (empty($email) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        // البحث عن المستخدم في قاعدة البيانات
        $sql = "SELECT id, full_name, email, password, is_admin FROM members WHERE email = ?";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $stmt->store_result();

            if ($stmt->num_rows === 1) {
                $stmt->bind_result($id, $full_name, $email_db, $hashed_password, $is_admin);
                $stmt->fetch();

                // التحقق من كلمة المرور
                if (password_verify($password, $hashed_password)) {
                    // إنشاء الجلسة
                    $_SESSION['user_id'] = $id;
                    $_SESSION['user_name'] = $full_name;
                    $_SESSION['user_email'] = $email_db;
                    $_SESSION['is_admin'] = $is_admin;

                    // التوجيه
                    if ($is_admin) {
                        redirect('admin/dashboard.php');
                    } else {
                        redirect('member/dashboard.php');
                    }
                    exit;
                } else {
                    $error = 'كلمة المرور غير صحيحة';
                }
            } else {
                $error = 'البريد الإلكتروني غير مسجل';
            }

            $stmt->close();
        } else {
            $error = 'فشل في تنفيذ الاستعلام: ' . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نادي افانتي</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="login-container">
        <h2>تسجيل الدخول</h2>
        <?php if ($error): ?>
            <div class="error"><?= $error ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="btn">دخول</button>
        </form>

        <p>ليس لديك حساب؟ <a href="register.php">تسجيل جديد</a></p>
    </div>
</body>
</html>
