<?php
$page_title = "إدارة المدفوعات";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// معالجة تحديث حالة الدفعة
if (isset($_GET['update_status']) && is_numeric($_GET['update_status']) && isset($_GET['status'])) {
    $payment_id = (int)$_GET['update_status'];
    $new_status = sanitize_input($_GET['status']);

    $allowed_statuses = ['pending', 'completed', 'failed', 'refunded'];
    if (in_array($new_status, $allowed_statuses)) {
        $update_sql = "UPDATE payments SET status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("si", $new_status, $payment_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث حالة الدفعة بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث حالة الدفعة";
        }
        $update_stmt->close();
    } else {
        $_SESSION['error'] = "حالة الدفعة غير صحيحة";
    }

    redirect('index.php');
    exit;
}

// البحث والفلترة
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';
$method_filter = isset($_GET['method']) ? sanitize_input($_GET['method']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_input($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_input($_GET['date_to']) : '';

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(m.full_name LIKE ? OR m.email LIKE ? OR p.notes LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= 'sss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($method_filter)) {
    $where_conditions[] = "p.payment_method = ?";
    $params[] = $method_filter;
    $param_types .= 's';
}

if (!empty($date_from)) {
    $where_conditions[] = "p.payment_date >= ?";
    $params[] = $date_from;
    $param_types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "p.payment_date <= ?";
    $params[] = $date_to;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// إحصائيات المدفوعات
$stats_sql = "SELECT
                COUNT(*) as total_payments,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_completed,
                SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as total_pending,
                SUM(CASE WHEN status = 'failed' THEN amount ELSE 0 END) as total_failed,
                SUM(CASE WHEN status = 'refunded' THEN amount ELSE 0 END) as total_refunded
              FROM payments p
              JOIN members m ON p.member_id = m.id
              $where_clause";

$stats_stmt = $conn->prepare($stats_sql);
if (!empty($params)) {
    $stats_stmt->bind_param($param_types, ...$params);
}
$stats_stmt->execute();
$stats_stmt->bind_result($total_payments, $total_amount, $completed_amount, $pending_amount);
$stats_stmt->fetch();
$stats = [
    'total_payments' => $total_payments,
    'total_amount' => $total_amount,
    'completed_amount' => $completed_amount,
    'pending_amount' => $pending_amount
];
$stats_stmt->close();

// الصفحات
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 15;
$offset = ($page - 1) * $per_page;

// جلب المدفوعات
$sql = "SELECT p.*, m.full_name, m.email, sp.name as plan_name
        FROM payments p
        JOIN members m ON p.member_id = m.id
        LEFT JOIN subscriptions s ON p.subscription_id = s.id
        LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
        $where_clause
        ORDER BY p.payment_date DESC, p.created_at DESC
        LIMIT $per_page OFFSET $offset";

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$payments = $stmt->get_result();
$stmt->close();

// عدد الصفحات
$count_sql = "SELECT COUNT(*) as total FROM payments p JOIN members m ON p.member_id = m.id $where_clause";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_payments = $count_stmt->get_result()->fetch_assoc()['total'];
$count_stmt->close();
$total_pages = ceil($total_payments / $per_page);

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إدارة المدفوعات</h2>
    <a href="add.php" class="btn"><i class="fas fa-plus"></i> إضافة دفعة جديدة</a>
</div>

<!-- إحصائيات المدفوعات -->
<div class="dashboard-cards" style="margin-bottom: 30px;">
    <div class="card">
        <h3>إجمالي المدفوعات</h3>
        <div class="number"><?php echo $stats['total_payments']; ?></div>
    </div>
    <div class="card">
        <h3>المدفوعات المكتملة</h3>
        <div class="number" style="color: var(--success-color);"><?php echo number_format($stats['total_completed'], 2); ?> ر.س</div>
    </div>
    <div class="card">
        <h3>المدفوعات المعلقة</h3>
        <div class="number" style="color: var(--warning-color);"><?php echo number_format($stats['total_pending'], 2); ?> ر.س</div>
    </div>
    <div class="card">
        <h3>المدفوعات المرتدة</h3>
        <div class="number" style="color: var(--error-color);"><?php echo number_format($stats['total_refunded'], 2); ?> ر.س</div>
    </div>
</div>

<!-- نموذج البحث والفلترة -->
<div class="table-container" style="margin-bottom: 20px;">
    <form method="GET" style="padding: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end;">
        <div class="form-group" style="margin-bottom: 0;">
            <label for="search">البحث:</label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="اسم العضو أو البريد الإلكتروني">
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="status">حالة الدفعة:</label>
            <select id="status" name="status">
                <option value="">جميع الحالات</option>
                <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>معلقة</option>
                <option value="failed" <?php echo $status_filter == 'failed' ? 'selected' : ''; ?>>فاشلة</option>
                <option value="refunded" <?php echo $status_filter == 'refunded' ? 'selected' : ''; ?>>مرتدة</option>
            </select>
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="method">طريقة الدفع:</label>
            <select id="method" name="method">
                <option value="">جميع الطرق</option>
                <option value="cash" <?php echo $method_filter == 'cash' ? 'selected' : ''; ?>>نقداً</option>
                <option value="card" <?php echo $method_filter == 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                <option value="bank_transfer" <?php echo $method_filter == 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                <option value="online" <?php echo $method_filter == 'online' ? 'selected' : ''; ?>>دفع إلكتروني</option>
            </select>
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="date_from">من تاريخ:</label>
            <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="date_to">إلى تاريخ:</label>
            <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
        </div>

        <div style="display: flex; gap: 10px;">
            <button type="submit" class="btn"><i class="fas fa-search"></i> بحث</button>
            <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-times"></i> إلغاء</a>
        </div>
    </form>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الرقم</th>
                <th>العضو</th>
                <th>نوع الاشتراك</th>
                <th>المبلغ</th>
                <th>طريقة الدفع</th>
                <th>تاريخ الدفع</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($payments->num_rows > 0): ?>
                <?php while ($payment = $payments->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $payment['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($payment['full_name']); ?></strong><br>
                            <small style="color: var(--gray-color);"><?php echo htmlspecialchars($payment['email']); ?></small>
                        </td>
                        <td><?php echo $payment['plan_name'] ? htmlspecialchars($payment['plan_name']) : 'غير محدد'; ?></td>
                        <td><strong><?php echo number_format($payment['amount'], 2); ?> ر.س</strong></td>
                        <td>
                            <?php
                            switch($payment['payment_method']) {
                                case 'cash': echo '<i class="fas fa-money-bill"></i> نقداً'; break;
                                case 'card': echo '<i class="fas fa-credit-card"></i> بطاقة ائتمان'; break;
                                case 'bank_transfer': echo '<i class="fas fa-university"></i> تحويل بنكي'; break;
                                case 'online': echo '<i class="fas fa-globe"></i> دفع إلكتروني'; break;
                                default: echo $payment['payment_method']; break;
                            }
                            ?>
                        </td>
                        <td><?php echo $payment['payment_date']; ?></td>
                        <td>
                            <span class="status <?php echo $payment['status']; ?>">
                                <?php
                                switch($payment['status']) {
                                    case 'completed': echo 'مكتملة'; break;
                                    case 'pending': echo 'معلقة'; break;
                                    case 'failed': echo 'فاشلة'; break;
                                    case 'refunded': echo 'مرتدة'; break;
                                }
                                ?>
                            </span>
                        </td>
                        <td class="actions">
                            <a href="view.php?id=<?php echo $payment['id']; ?>" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                <i class="fas fa-eye"></i> عرض
                            </a>

                            <?php if ($payment['status'] == 'pending'): ?>
                                <a href="?update_status=<?php echo $payment['id']; ?>&status=completed"
                                   class="btn btn-success" style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')">
                                    <i class="fas fa-check"></i> تأكيد
                                </a>
                                <a href="?update_status=<?php echo $payment['id']; ?>&status=failed"
                                   class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من رفض هذه الدفعة؟')">
                                    <i class="fas fa-times"></i> رفض
                                </a>
                            <?php elseif ($payment['status'] == 'completed'): ?>
                                <a href="?update_status=<?php echo $payment['id']; ?>&status=refunded"
                                   class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من استرداد هذه الدفعة؟')">
                                    <i class="fas fa-undo"></i> استرداد
                                </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                        <i class="fas fa-money-bill" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                        <p>لا توجد مدفوعات مطابقة لمعايير البحث</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- الصفحات -->
<?php if ($total_pages > 1): ?>
    <div style="text-align: center; margin-top: 20px;">
        <?php
        $query_params = $_GET;
        for ($i = 1; $i <= $total_pages; $i++):
            $query_params['page'] = $i;
            $query_string = http_build_query($query_params);
        ?>
            <a href="?<?php echo $query_string; ?>"
               class="btn <?php echo $i == $page ? 'btn-success' : ''; ?>"
               style="margin: 0 5px; padding: 8px 12px;">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
    </div>
<?php endif; ?>

<?php include_once '../../includes/footer.php'; ?>