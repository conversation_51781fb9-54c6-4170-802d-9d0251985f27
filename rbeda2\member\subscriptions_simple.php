<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: ../index.php");
    exit;
}

require_once '../includes/config.php';

$user_id = $_SESSION['user_id'];
$page_title = "اشتراكاتي";

// معالجة طلب اشتراك جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['subscribe'])) {
    $plan_id = (int)$_POST['plan_id'];
    
    // التحقق من وجود الخطة وأنها نشطة
    $plan_sql = "SELECT * FROM subscription_plans WHERE id = $plan_id AND is_active = 1";
    $plan_result = mysqli_query($conn, $plan_sql);
    $plan = mysqli_fetch_assoc($plan_result);
    
    if ($plan) {
        // حساب السعر النهائي
        $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
        
        // تواريخ الاشتراك
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime("+{$plan['duration_months']} months"));
        
        // إنهاء أي اشتراك نشط حالي
        $update_current_sql = "UPDATE subscriptions SET status = 'expired' WHERE member_id = $user_id AND status = 'active'";
        mysqli_query($conn, $update_current_sql);
        
        // إضافة الاشتراك الجديد
        $insert_subscription_sql = "INSERT INTO subscriptions (member_id, plan_id, start_date, end_date, status, amount_paid) VALUES ($user_id, $plan_id, '$start_date', '$end_date', 'active', $final_price)";
        
        if (mysqli_query($conn, $insert_subscription_sql)) {
            $subscription_id = mysqli_insert_id($conn);
            
            // إضافة سجل الدفعة
            $payment_sql = "INSERT INTO payments (member_id, subscription_id, amount, payment_method, payment_date, status) VALUES ($user_id, $subscription_id, $final_price, 'cash', '$start_date', 'completed')";
            mysqli_query($conn, $payment_sql);
            
            $_SESSION['success'] = 'تم الاشتراك بنجاح! مرحباً بك في ' . $plan['name'];
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء الاشتراك. الرجاء المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = 'خطة الاشتراك غير متاحة';
    }
    
    header("Location: subscriptions_simple.php");
    exit;
}

// جلب الاشتراك النشط الحالي
$current_subscription_sql = "SELECT s.*, sp.name as plan_name, sp.duration_months 
                            FROM subscriptions s 
                            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id 
                            WHERE s.member_id = $user_id AND s.status = 'active' 
                            ORDER BY s.end_date DESC LIMIT 1";
$current_result = mysqli_query($conn, $current_subscription_sql);
$current_subscription = mysqli_fetch_assoc($current_result);

// حساب الأيام المتبقية
$days_remaining = 0;
if ($current_subscription) {
    $end_date = new DateTime($current_subscription['end_date']);
    $today = new DateTime();
    $interval = $today->diff($end_date);
    $days_remaining = $interval->days;
    if ($today > $end_date) {
        $days_remaining = -$days_remaining;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نادي أفانتي</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                نادي أفانتي
            </div>
            <ul class="nav-links">
                <li><a href="dashboard_simple.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="profile_simple.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                <li><a href="subscriptions_simple.php"><i class="fas fa-credit-card"></i> اشتراكاتي</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <?php if (isset($_SESSION['error'])): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <div class="page-header">
            <h2>اشتراكاتي</h2>
            <a href="dashboard_simple.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للوحة التحكم</a>
        </div>

        <!-- الاشتراك الحالي -->
        <?php if ($current_subscription): ?>
            <div class="dashboard-header" style="margin-bottom: 30px;">
                <h3>اشتراكك الحالي</h3>
                <div style="display: flex; gap: 20px; margin-top: 15px; flex-wrap: wrap;">
                    <div class="card" style="flex: 1; min-width: 200px;">
                        <h4><?php echo htmlspecialchars($current_subscription['plan_name'] ?: 'غير محدد'); ?></h4>
                        <p><strong>من:</strong> <?php echo $current_subscription['start_date']; ?></p>
                        <p><strong>إلى:</strong> <?php echo $current_subscription['end_date']; ?></p>
                        <p><strong>المبلغ:</strong> <?php echo number_format($current_subscription['amount_paid'], 2); ?> ر.س</p>
                        <?php if ($days_remaining >= 0): ?>
                            <p style="color: var(--success-color);"><strong>متبقي:</strong> <?php echo $days_remaining; ?> يوم</p>
                        <?php else: ?>
                            <p style="color: var(--error-color);"><strong>منتهي منذ:</strong> <?php echo abs($days_remaining); ?> يوم</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- خطط الاشتراك المتاحة -->
        <div class="page-header">
            <h3><?php echo $current_subscription ? 'تجديد الاشتراك أو تغيير الخطة' : 'اختر خطة الاشتراك'; ?></h3>
        </div>

        <div class="subscription-plans">
            <?php
            $plans_sql = "SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY duration_months ASC";
            $plans_result = mysqli_query($conn, $plans_sql);
            
            if ($plans_result && mysqli_num_rows($plans_result) > 0):
                while ($plan = mysqli_fetch_assoc($plans_result)):
                    $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
                    $is_popular = $plan['duration_months'] == 3; // اجعل الخطة ربع السنوية الأكثر شعبية
            ?>
                <div class="plan-card <?php echo $is_popular ? 'popular' : ''; ?>">
                    <?php if ($is_popular): ?>
                        <div class="popular-badge">الأكثر شعبية</div>
                    <?php endif; ?>
                    
                    <h3><?php echo htmlspecialchars($plan['name']); ?></h3>
                    
                    <div class="price">
                        <?php echo number_format($final_price, 2); ?> ر.س
                    </div>
                    
                    <?php if ($plan['discount_percentage'] > 0): ?>
                        <div class="discount">خصم <?php echo $plan['discount_percentage']; ?>%</div>
                        <p style="text-decoration: line-through; color: var(--gray-color);">
                            <?php echo number_format($plan['price'], 2); ?> ر.س
                        </p>
                    <?php endif; ?>
                    
                    <ul>
                        <li>مدة الاشتراك: <?php echo $plan['duration_months']; ?> <?php echo $plan['duration_months'] == 1 ? 'شهر' : 'أشهر'; ?></li>
                        <li>الوصول لجميع المرافق</li>
                        <li>استخدام الأجهزة الرياضية</li>
                        <li>حصص اللياقة البدنية</li>
                        <?php if ($plan['duration_months'] >= 3): ?>
                            <li>استشارة مجانية مع المدرب</li>
                        <?php endif; ?>
                        <?php if ($plan['duration_months'] >= 6): ?>
                            <li>برنامج تغذية مخصص</li>
                        <?php endif; ?>
                        <?php if ($plan['duration_months'] >= 12): ?>
                            <li>متابعة شخصية مع المدرب</li>
                            <li>تقييم دوري للتقدم</li>
                        <?php endif; ?>
                    </ul>
                    
                    <?php if ($plan['description']): ?>
                        <p style="color: var(--gray-color); font-size: 14px; margin: 15px 0;">
                            <?php echo htmlspecialchars($plan['description']); ?>
                        </p>
                    <?php endif; ?>
                    
                    <form method="POST" style="margin-top: 20px;">
                        <input type="hidden" name="plan_id" value="<?php echo $plan['id']; ?>">
                        <button type="submit" name="subscribe" class="btn" 
                                onclick="return confirm('هل أنت متأكد من الاشتراك في <?php echo htmlspecialchars($plan['name']); ?>؟')">
                            <i class="fas fa-credit-card"></i> 
                            <?php echo $current_subscription ? 'تجديد/تغيير' : 'اشترك الآن'; ?>
                        </button>
                    </form>
                </div>
            <?php 
                endwhile;
            else: 
            ?>
                <div style="text-align: center; padding: 40px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--warning-color); margin-bottom: 15px;"></i>
                    <p>لا توجد خطط اشتراك متاحة حالياً</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- تاريخ الاشتراكات -->
        <div class="page-header" style="margin-top: 50px;">
            <h3>تاريخ اشتراكاتي</h3>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>نوع الاشتراك</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الانتهاء</th>
                        <th>المبلغ المدفوع</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // جلب جميع اشتراكات العضو
                    $all_subscriptions_sql = "SELECT s.*, sp.name as plan_name
                                             FROM subscriptions s 
                                             LEFT JOIN subscription_plans sp ON s.plan_id = sp.id 
                                             WHERE s.member_id = $user_id 
                                             ORDER BY s.created_at DESC";
                    $all_subscriptions_result = mysqli_query($conn, $all_subscriptions_sql);
                    
                    if ($all_subscriptions_result && mysqli_num_rows($all_subscriptions_result) > 0):
                        while ($subscription = mysqli_fetch_assoc($all_subscriptions_result)):
                            $status_class = $subscription['status'];
                            $status_text = '';
                            switch($subscription['status']) {
                                case 'active': $status_text = 'نشط'; break;
                                case 'expired': $status_text = 'منتهي'; break;
                                case 'cancelled': $status_text = 'ملغي'; break;
                            }
                    ?>
                        <tr>
                            <td><?php echo htmlspecialchars($subscription['plan_name'] ?: 'غير محدد'); ?></td>
                            <td><?php echo $subscription['start_date']; ?></td>
                            <td><?php echo $subscription['end_date']; ?></td>
                            <td><?php echo number_format($subscription['amount_paid'], 2); ?> ر.س</td>
                            <td><span class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                        </tr>
                    <?php 
                        endwhile;
                    else: 
                    ?>
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 40px;">
                                <i class="fas fa-history" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                                <p>لا يوجد تاريخ اشتراكات بعد</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

    </div>

    <footer style="background-color: var(--secondary-color); color: white; text-align: center; padding: 20px 0; margin-top: 50px;">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> نادي أفانتي. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
</body>
</html>
