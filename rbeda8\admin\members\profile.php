<?php
$page_title = "الملف الشخصي";
require_once '../includes/config.php';
require_once '../includes/auth.php';

$user_id = $_SESSION['user_id'];
$user = mysqli_fetch_assoc(mysqli_query($conn, "SELECT * FROM members WHERE id = $user_id"));

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['full_name']);
    $phone = sanitize_input($_POST['phone_number']);
    $password = sanitize_input($_POST['password']);
    $confirm_password = sanitize_input($_POST['confirm_password']);
    
    if (!empty($password) && $password !== $confirm_password) {
        $error = 'كلمتا المرور غير متطابقتين';
    } else {
        $sql = "UPDATE members SET full_name = ?, phone_number = ?";
        $params = [$name, $phone];
        
        if (!empty($password)) {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $sql .= ", password = ?";
            $params[] = $hashed_password;
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $user_id;
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
        
        if ($stmt->execute()) {
            $_SESSION['user_name'] = $name;
            $success = 'تم تحديث الملف الشخصي بنجاح';
        } else {
            $error = 'حدث خطأ أثناء التحديث: ' . $conn->error;
        }
    }
}

include_once '../includes/header.php';
?>

<div class="form-container">
    <h2>الملف الشخصي</h2>
    
    <?php if ($error): ?>
        <div class="error"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <form method="POST">
        <div class="form-group">
            <label for="full_name">الاسم الكامل:</label>
            <input type="text" id="full_name" name="full_name" value="<?php echo $user['full_name']; ?>" required>
        </div>
        
        <div class="form-group">
            <label for="email">البريد الإلكتروني:</label>
            <input type="email" id="email" name="email" value="<?php echo $user['email']; ?>" disabled>
            <small class="form-text">لا يمكن تغيير البريد الإلكتروني</small>
        </div>
        
        <div class="form-group">
            <label for="phone_number">رقم الهاتف:</label>
            <input type="tel" id="phone_number" name="phone_number" value="<?php echo $user['phone_number']; ?>">
        </div>
        
        <div class="form-group">
            <label for="join_date">تاريخ الانضمام:</label>
            <input type="text" id="join_date" value="<?php echo $user['join_date']; ?>" disabled>
        </div>
        
        <div class="form-group">
            <label for="password">كلمة المرور الجديدة (اتركها فارغة إذا لم ترغب في التغيير):</label>
            <input type="password" id="password" name="password">
        </div>
        
        <div class="form-group">
            <label for="confirm_password">تأكيد كلمة المرور الجديدة:</label>
            <input type="password" id="confirm_password" name="confirm_password">
        </div>
        
        <button type="submit" class="btn">حفظ التغييرات</button>
    </form>
</div>

<?php include_once '../includes/footer.php'; ?>